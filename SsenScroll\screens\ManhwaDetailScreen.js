import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  Dimensions,
  ActivityIndicator,
  StatusBar,
  PanResponder,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import Animated, { useSharedValue, useAnimatedStyle, withSpring, withTiming, runOnJS } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { comickAPI, getCoverImageUrl, formatStatus } from '../services/api';
import { storageService } from '../services/storageService';

const { width } = Dimensions.get('window');
const TABS = ['Chapters', 'Details', 'Similar'];

// --- Helper Functions ---
const formatFollowCount = (count) => {
  if (!count) return '0';
  if (count < 1000) return count.toString();
  return `${(count / 1000).toFixed(1).replace('.0', '')}k`;
};

// --- Reusable UI Components ---

const TopNav = React.memo(({ onBack, insets, isFavorited, onToggleFavorite, isTogglingFavorite }) => (
  <View style={[styles.topNavContainer, { top: insets.top }]}>
    <View style={styles.topNav}>
      <TouchableOpacity style={styles.iconButton} onPress={onBack}>
        <Ionicons name="arrow-back" size={24} color="#FFF" />
      </TouchableOpacity>
      <View style={{ flexDirection: 'row' }}>
        <TouchableOpacity
          style={[styles.iconButton, isTogglingFavorite && styles.iconButtonDisabled]}
          onPress={onToggleFavorite}
          disabled={isTogglingFavorite}
        >
          <Ionicons name={isFavorited ? "heart" : "heart-outline"} size={24} color={isFavorited ? "#FF6B6B" : "#FFF"} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.iconButton}>
          <Ionicons name="share-social-outline" size={24} color="#FFF" />
        </TouchableOpacity>
      </View>
    </View>
  </View>
));

const ComicHeader = React.memo(({ comic, authorName, onContinue, readingProgress }) => (
  <View style={styles.headerContent}>
    <Image
      source={{ uri: getCoverImageUrl(comic?.md_covers?.[0]) }}
      style={styles.coverImage}
      placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
      contentFit="cover"
      transition={0}
    />
    <View style={styles.headerInfo}>
        <View> 
            <Text style={styles.title} numberOfLines={3}>{comic?.title}</Text>
            <Text style={styles.author}>By {authorName || 'Unknown'}</Text>
        </View>
        <View>
            <View style={styles.statsRow}>
                <Ionicons name="flag" size={14} color="#FFC107" /><Text style={styles.statText}>{formatStatus(comic?.status)}</Text>
            </View>
            <View style={styles.statsRow}>
                <Ionicons name="star" size={14} color="#FFC107" /><Text style={styles.statText}>{parseFloat(comic?.bayesian_rating || 0).toFixed(1)}</Text>
                <Ionicons name="people" size={14} color="#FF6B6B" style={{ marginLeft: 15 }} /><Text style={styles.statText}>{formatFollowCount(comic?.user_follow_count)}</Text>
            </View>
            {readingProgress?.progressPercentage > 0 && (
                <View style={styles.progressInfo}>
                    <View style={styles.progressBarContainer}>
                    <View style={[styles.progressBarFill, { width: `${readingProgress.progressPercentage}%` }]} />
                    </View>
                    <Text style={styles.progressPercentage}>{readingProgress.progressPercentage}% complete</Text>
                </View>
            )}
        </View>
        <TouchableOpacity style={styles.continueButton} onPress={onContinue}>
            <Text style={styles.continueButtonText}>
            {readingProgress ? `Continue Ch. ${readingProgress.lastChapterNumber}` : 'Start Reading'}
            </Text>
        </TouchableOpacity>
    </View>
  </View>
));

const CustomTabBar = React.memo(({ activeTab, onTabPress, chapterCount }) => {
  const translateX = useSharedValue(0);
  useEffect(() => {
    const newIndex = TABS.indexOf(activeTab);
    translateX.value = withSpring(newIndex * (width / TABS.length), { damping: 15, stiffness: 120 });
  }, [activeTab]);
  const animatedIndicatorStyle = useAnimatedStyle(() => ({ transform: [{ translateX: translateX.value }] }));
  return (
    <View style={styles.tabBarContainer}>
      <View style={styles.tabBar}>
        {TABS.map((tab) => (
          <TouchableOpacity key={tab} style={styles.tab} onPress={() => onTabPress(tab)}>
            <Text style={[styles.tabText, activeTab === tab && styles.tabTextActive]}>
              {tab} {tab === 'Chapters' && `(${chapterCount})`}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      <Animated.View style={[styles.tabIndicator, animatedIndicatorStyle]} />
    </View>
  );
});

const MemoizedHeader = React.memo(({
    insets, comicDetails, handleContinue, readingProgress, activeTab, handleTabPress, groupedChapters
}) => (
    <>
      <View style={{ paddingTop: insets.top + 50 }} />
      <ComicHeader
        comic={comicDetails?.comic}
        authorName={comicDetails?.authors?.[0]?.name}
        onContinue={handleContinue}
        readingProgress={readingProgress}
      />
      <CustomTabBar
        activeTab={activeTab}
        onTabPress={handleTabPress}
        chapterCount={groupedChapters.length}
      />
    </>
));

// FIX: Corrected callback logic to prevent 'dismiss is not a function' error.
const CustomToast = ({ message, onDismiss, isVisible }) => {
    const translateY = useSharedValue(-150);

    const pan = useRef(
        PanResponder.create({
            onMoveShouldSetPanResponder: (_, g) => Math.abs(g.dx) > 5,
            onPanResponderMove: (_, g) => {
                if (g.dx < -5) { // Swipe left
                     translateY.value = withTiming(-150, { duration: 200 }, (finished) => {
                         if (finished) runOnJS(onDismiss)();
                     });
                }
            },
        })
    ).current;

    useEffect(() => {
        let timer;
        if (isVisible) {
            translateY.value = withSpring(0, { damping: 18, stiffness: 120 });
            timer = setTimeout(() => {
                translateY.value = withTiming(-150, { duration: 300 }, (finished) => {
                    if (finished) runOnJS(onDismiss)();
                });
            }, 4000);
        }
        return () => clearTimeout(timer);
    }, [isVisible, onDismiss]);

    const animatedStyle = useAnimatedStyle(() => ({ transform: [{ translateY: translateY.value }] }));

    const handleDismissPress = () => {
        translateY.value = withTiming(-150, { duration: 200 }, (finished) => {
            if (finished) runOnJS(onDismiss)();
        });
    };

    if (!isVisible) return null;

    return (
        <Animated.View style={[styles.toastBaseContainer, animatedStyle]} {...pan.panHandlers}>
            <View style={styles.toastPill}>
                <Text style={styles.toastMessage} numberOfLines={2}>{message}</Text>
                 <TouchableOpacity style={styles.toastCloseButton} onPress={handleDismissPress}>
                    <Ionicons name="close" size={20} color="#FFF" />
                </TouchableOpacity>
            </View>
        </Animated.View>
    );
};


const DetailsContent = ({ comic }) => ( /* ... (unchanged) ... */ <View style={styles.contentSection}>
    <Text style={styles.sectionTitle}>Description</Text>
    <Text style={styles.description}>{comic?.desc || "No description available."}</Text>
    <Text style={styles.sectionTitle}>Genres</Text>
    <View style={styles.genresContainer}>
      {comic?.md_comic_md_genres?.map((genreItem, index) => (
        <View key={index} style={styles.genreChip}><Text style={styles.genreText}>{genreItem.md_genres.name}</Text></View>
      ))}
    </View>
  </View>);

const SimilarItem = ({ comic, onNavigate }) => ( /* ... (unchanged) ... */  <TouchableOpacity style={styles.similarCard} onPress={() => onNavigate(comic)}>
        <Image 
            source={{uri: getCoverImageUrl(comic?.md_covers?.[0])}}
            style={styles.similarImage}
            placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj["
        />
        <Text style={styles.similarTitle} numberOfLines={2}>{comic.title}</Text>
    </TouchableOpacity>);

const SimilarContent = ({ recommendations, onNavigate }) => { /* ... (unchanged) ... */ 
    const similarComics = recommendations?.map(rec => rec.relates) || [];
    if (similarComics.length === 0) {
        return <View style={styles.contentSection}><Text style={styles.description}>No similar comics found.</Text></View>
    }
    return (
        <View style={styles.contentSection}>
             <Text style={styles.sectionTitle}>Similar Comics</Text>
             <View style={styles.similarGrid}>
                {similarComics.map(comic => comic ? <SimilarItem key={comic.hid} comic={comic} onNavigate={onNavigate} /> : null)}
             </View>
        </View>
    );
};

const ChapterGroupItem = ({ group, onNavigate }) => { /* ... (unchanged) ... */ 
    const [isExpanded, setExpanded] = useState(false);
    const hasMultiple = group.chapters.length > 1;

    return (
        <View style={styles.chapterGroupContainer}>
            <View style={styles.chapterItem}>
                <TouchableOpacity style={styles.chapterTitleButton} onPress={() => onNavigate(group.defaultChapter)}>
                    <View>
                        <Text style={styles.chapterTitle}>Chapter {group.chapterNumber}</Text>
                        <Text style={styles.chapterDate}>{new Date(group.defaultChapter.created_at).toLocaleDateString()}</Text>
                    </View>
                </TouchableOpacity>
                {hasMultiple && (
                    <TouchableOpacity style={styles.scanGroupToggle} onPress={() => setExpanded(!isExpanded)}>
                         <Ionicons name={isExpanded ? "close-circle-outline" : "ellipsis-vertical"} size={22} color="#8A8899" />
                    </TouchableOpacity>
                )}
            </View>
            {isExpanded && hasMultiple && (
                <View style={styles.expandedScansContainer}>
                    {group.chapters.map(chapter => (
                        <TouchableOpacity key={chapter.hid} style={styles.scanItem} onPress={() => onNavigate(chapter)}>
                            <Text style={styles.scanGroupName}>{chapter.group_name?.[0] || 'Unknown'}</Text>
                            <Text style={styles.scanDate}>{new Date(chapter.created_at).toLocaleDateString()}</Text>
                        </TouchableOpacity>
                    ))}
                </View>
            )}
        </View>
    )
}

const LoadingScreen = () => (<View style={styles.fullScreenLoader}><ActivityIndicator size="large" color="#FFF" /></View>);

export default function ManhwaDetailScreen({ route, navigation }) {
  const { slug, hid } = route.params;
  const [comicDetails, setComicDetails] = useState(null);
  const [groupedChapters, setGroupedChapters] = useState([]);
  const [allChaptersRaw, setAllChaptersRaw] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('Chapters');
  const [isFavorited, setIsFavorited] = useState(false);
  const [isTogglingFavorite, setIsTogglingFavorite] = useState(false);
  const [readingProgress, setReadingProgress] = useState(null);
  const [toastConfig, setToastConfig] = useState({ isVisible: false, message: '' });

  const insets = useSafeAreaInsets();

  useEffect(() => {
    const loadAllData = async () => {
      try {
        setLoading(true);
        const comicData = await comickAPI.getComicDetails(slug);
        setComicDetails(comicData);
        
        const [favoriteStatus, progressData] = await Promise.all([
            storageService.isFavorited(slug),
            storageService.getReadingProgress(slug)
        ]);
        setIsFavorited(favoriteStatus);
        setReadingProgress(progressData);
        const comicHid = hid || comicData?.comic?.hid;
        if (comicHid) {
          const chaptersData = await comickAPI.getComicChapters(comicHid, { limit: 2000, lang: 'en' });
          const englishChapters = chaptersData.chapters || [];
          setAllChaptersRaw(englishChapters);
          const chapterGroups = englishChapters.reduce((acc, chapter) => {
            const chapNum = chapter.chap || 'Unknown';
            if (!acc[chapNum]) acc[chapNum] = [];
            acc[chapNum].push(chapter);
            return acc;
          }, {});
          const groupedChaptersArray = Object.entries(chapterGroups)
            .map(([chapNum, chapters]) => ({
              chapterNumber: chapNum,
              chapters: chapters.sort((a, b) => new Date(b.created_at) - new Date(a.created_at)),
              defaultChapter: chapters[0]
            })).sort((a, b) => parseFloat(b.chapterNumber) - parseFloat(a.chapterNumber));
          setGroupedChapters(groupedChaptersArray);
        }
      } catch (error) { Alert.alert('Error', 'Failed to load comic details.'); } 
      finally { setLoading(false); }
    };
    if (slug) {
        loadAllData();
    }
  }, [slug, hid]);
  
  const showToast = useCallback((message) => {
    setToastConfig({ isVisible: true, message });
  }, []);

  const dismissToast = useCallback(() => {
    setToastConfig(config => ({ ...config, isVisible: false }));
  }, []);

  const navigateToChapterReader = (chapter) => {
    if (!chapter) { Alert.alert("Error", "Chapter data is not available."); return; }
    navigation.navigate('ChapterReader', {
        currentChapter: chapter,
        allChapters: [...allChaptersRaw].sort((a, b) => parseFloat(a.chap) - parseFloat(b.chap)),
        manhwaInfo: {
          slug: comicDetails?.comic?.slug,
          title: comicDetails?.comic?.title,
          cover: comicDetails?.comic?.md_covers?.[0],
        }
    });
  };
  
  const navigateToSimilarDetail = (similarComic) => {
      navigation.push('ManhwaDetail', { slug: similarComic.slug, hid: similarComic.hid });
  };
  
  const handleContinue = useCallback(() => {
    if (allChaptersRaw.length === 0) { Alert.alert("No Chapters", "No chapters are available yet."); return; }
    if (readingProgress?.lastChapterHid) {
      const lastReadChapter = allChaptersRaw.find(ch => ch.hid === readingProgress.lastChapterHid);
      if (lastReadChapter) { navigateToChapterReader(lastReadChapter); return; }
    }
    const firstChapterGroup = groupedChapters[groupedChapters.length - 1];
    if (firstChapterGroup?.defaultChapter) {
      navigateToChapterReader(firstChapterGroup.defaultChapter);
    } else {
      Alert.alert("No Chapters", "Could not find the first chapter.");
    }
  }, [allChaptersRaw, groupedChapters, readingProgress, navigation]);

  const handleToggleFavorite = useCallback(async () => {
    if (isTogglingFavorite || !comicDetails?.comic) return;
    setIsTogglingFavorite(true);
    try {
      const manhwaData = { slug: comicDetails.comic.slug, hid: comicDetails.comic.hid, title: comicDetails.comic.title, md_covers: comicDetails.comic.md_covers, };
      const newFavoriteStatus = await storageService.toggleFavorite(manhwaData);
      setIsFavorited(newFavoriteStatus);
      const message = newFavoriteStatus ? 'Added to Favorites' : 'Removed from Favorites';
      showToast(message);
    } catch (error) { Alert.alert('Error', 'Failed to update favorites.'); } 
    finally { setIsTogglingFavorite(false); }
  }, [isTogglingFavorite, comicDetails, showToast]);
  
  const handleTabPress = useCallback((tab) => {
      setActiveTab(tab);
  }, []);

  if (loading || !comicDetails) return <LoadingScreen />;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <Image source={{ uri: getCoverImageUrl(comicDetails?.comic?.md_covers?.[1] || comicDetails?.comic?.md_covers?.[0]) }} style={styles.backgroundImage} blurRadius={25} />
      <View style={styles.backgroundOverlay} />
      
      <FlatList
          ListHeaderComponent={
            <MemoizedHeader 
                insets={insets} comicDetails={comicDetails} handleContinue={handleContinue}
                readingProgress={readingProgress} activeTab={activeTab} handleTabPress={handleTabPress}
                groupedChapters={groupedChapters}
            />
          }
          data={activeTab === 'Chapters' ? groupedChapters.slice().sort((a,b) => parseFloat(b.chapterNumber) - parseFloat(a.chapterNumber)) : []}
          renderItem={({ item }) => <ChapterGroupItem group={item} onNavigate={navigateToChapterReader} />}
          keyExtractor={(item) => item.chapterNumber}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 40 }}
          ListFooterComponent={
              <>
                  {activeTab === 'Details' && <DetailsContent comic={comicDetails?.comic} />}
                  {activeTab === 'Similar' && <SimilarContent recommendations={comicDetails?.comic?.recommendations} onNavigate={navigateToSimilarDetail} />}
              </>
          }
      />

      <TopNav
        onBack={() => navigation.goBack()} insets={insets} isFavorited={isFavorited}
        onToggleFavorite={handleToggleFavorite} isTogglingFavorite={isTogglingFavorite}
      />
      
      <CustomToast 
        message={toastConfig.message}
        isVisible={toastConfig.isVisible}
        onDismiss={dismissToast}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#1F1D2B' },
  fullScreenLoader: { flex: 1, backgroundColor: '#1F1D2B', justifyContent: 'center', alignItems: 'center' },
  backgroundImage: { ...StyleSheet.absoluteFillObject, opacity: 0.3 },
  backgroundOverlay: { ...StyleSheet.absoluteFillObject, backgroundColor: 'rgba(31, 29, 43, 0.85)' },
  topNavContainer: { position: 'absolute', top: 0, left: 0, right: 0, zIndex: 10 },
  topNav: { flexDirection: 'row', justifyContent: 'space-between', paddingHorizontal: 15, paddingVertical: 10 },
  iconButton: { width: 44, height: 44, borderRadius: 22, backgroundColor: 'rgba(0,0,0,0.2)', justifyContent: 'center', alignItems: 'center' },
  iconButtonDisabled: { opacity: 0.5 },
  headerContent: { flexDirection: 'row', paddingHorizontal: 20, paddingTop: 10, paddingBottom: 20, marginBottom: 10, alignItems: 'flex-start', backgroundColor: 'transparent' },
  coverImage: { width: 120, height: 180, borderRadius: 12, backgroundColor: '#2D2A41' },
  headerInfo: { flex: 1, marginLeft: 20, height: 180, justifyContent: 'space-between' },
  title: { color: '#FFF', fontSize: 22, fontWeight: 'bold'},
  author: { color: '#A39DCE', fontSize: 14, marginTop: 4, marginBottom: 8 },
  statsRow: { flexDirection: 'row', alignItems: 'center', marginTop: 5 },
  statText: { color: '#FFF', fontSize: 14, fontWeight: '600', marginLeft: 6 },
  progressInfo: { marginTop: 8 },
  progressPercentage: { color: '#4CAF50', fontSize: 12, fontWeight: '500', marginTop: 5 },
  progressBarContainer: { height: 4, backgroundColor: 'rgba(255, 255, 255, 0.2)', borderRadius: 2, },
  progressBarFill: { height: '100%', backgroundColor: '#4CAF50', borderRadius: 2, },
  // FIX: Re-added marginTop to the continue button
  continueButton: { backgroundColor: '#5E5CE6', borderRadius: 25, height: 44, justifyContent: 'center', alignItems: 'center', marginTop: 15 },
  continueButtonText: { color: '#FFF', fontSize: 16, fontWeight: 'bold' },
  tabBarContainer: { backgroundColor: 'transparent' },
  tabBar: { flexDirection: 'row', borderBottomWidth: 1, borderBottomColor: '#2D2A41' },
  tab: { flex: 1, alignItems: 'center', paddingVertical: 15 },
  tabText: { color: '#8A8899', fontSize: 16, fontWeight: '500' },
  tabTextActive: { color: '#FFF', fontWeight: 'bold' },
  tabIndicator: { height: 3, width: width / TABS.length, backgroundColor: '#5E5CE6', position: 'absolute', bottom: -1 },
  contentSection: { padding: 20, backgroundColor: 'transparent', minHeight: 300 },
  sectionTitle: { color: '#FFF', fontSize: 20, fontWeight: 'bold', marginBottom: 15 },
  description: { color: '#D0CFD4', fontSize: 15, lineHeight: 22, marginBottom: 20 },
  genresContainer: { flexDirection: 'row', flexWrap: 'wrap' },
  genreChip: { backgroundColor: '#2D2A41', paddingHorizontal: 12, paddingVertical: 6, borderRadius: 15, marginRight: 10, marginBottom: 10 },
  genreText: { color: '#A39DCE', fontSize: 13, fontWeight: '500' },
  chapterGroupContainer: { borderBottomWidth: 1, borderBottomColor: '#2D2A41', paddingHorizontal: 20, backgroundColor: 'transparent' },
  chapterItem: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 12 },
  chapterTitleButton: { flex: 1 },
  chapterTitle: { color: '#EAEAEA', fontSize: 16, fontWeight: '500' },
  chapterDate: { color: '#8A8899', fontSize: 13, marginTop: 4 },
  scanGroupToggle: { padding: 8 },
  expandedScansContainer: { paddingLeft: 15, paddingBottom: 5, backgroundColor: 'rgba(0,0,0,0.2)' },
  scanItem: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 10, borderTopWidth: 1, borderTopColor: 'rgba(45, 42, 65, 0.5)' },
  scanGroupName: { color: '#A39DCE', fontSize: 14 },
  scanDate: { color: '#8A8899', fontSize: 13 },
  similarGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' },
  similarCard: { width: (width - 60) / 2, marginBottom: 20 },
  similarImage: { width: '100%', height: ((width - 60) / 2) * 1.5, borderRadius: 12, backgroundColor: '#333' },
  similarTitle: { color: '#FFF', fontSize: 15, fontWeight: '600', marginTop: 8 },
  toastBaseContainer: { position: 'absolute', top: 55, left: 0, right: 0, alignItems: 'center', zIndex: 9999, },
  toastPill: { flexDirection: 'row', alignItems: 'center', backgroundColor: 'rgba(45, 42, 65, 0.95)', borderRadius: 50, paddingVertical: 10, paddingLeft: 20, paddingRight: 10, width: '90%', minHeight: 50, },
  toastMessage: { flex: 1, color: '#FFF', fontSize: 15, fontWeight: '500' },
  toastCloseButton: { backgroundColor: 'rgba(0,0,0,0.2)', borderRadius: 15, width: 30, height: 30, justifyContent: 'center', alignItems: 'center', marginLeft: 10 }
});